import streamlit as st
import torch
from transformers import BertToken<PERSON>, BertForSequenceClassification
import re

# ==============================================================================
#  Load the fine-tuned model and tokenizer
# ==============================================================================
# Use st.cache_resource to load the model only once and save memory
@st.cache_resource
def load_model():
    # This path should point to the folder where your final model was saved
    model_path = "./final_dark_pattern_model"
    try:
        model = BertForSequenceClassification.from_pretrained(model_path)
        tokenizer = BertTokenizer.from_pretrained(model_path)
        return model, tokenizer
    except OSError:
        # If the model isn't found, display an error message
        st.error(f"Error: Model not found at path: {model_path}")
        st.error("Please make sure you have trained the model by running the training script and that the 'final_dark_pattern_model' folder exists in the same directory as this app.")
        return None, None

# Load the model and tokenizer
model, tokenizer = load_model()

# Set up the device (will use CPU as configured in training)
device = torch.device("cpu")
if model:
    model.to(device)

# ==============================================================================
#  Text cleaning and prediction functions
# ==============================================================================
# This text cleaning function MUST be identical to the one used for training
def clean_text(text):
    text = str(text).lower()
    text = re.sub(r'<.*?>', '', text)       # Remove HTML tags
    text = re.sub(r'[^a-z\s]', '', text)    # Remove punctuation and numbers
    tokens = text.split()                   # Split text into words
    return ' '.join(tokens)

def predict_dark_pattern(text):
    # If the model failed to load, return an error message
    if not model or not tokenizer:
        return "Model is not available."

    # Set the model to evaluation mode
    model.eval()

    # Clean the input text
    cleaned_text = clean_text(text)

    # Tokenize the text for BERT
    inputs = tokenizer(cleaned_text, return_tensors='pt', truncation=True, padding=True, max_length=128)

    # Move tensors to the correct device
    inputs = {k: v.to(device) for k, v in inputs.items()}

    # Perform prediction
    with torch.no_grad():
        outputs = model(**inputs)

    # Get the predicted class ID and the human-readable label
    prediction_id = torch.argmax(outputs.logits, dim=-1).item()
    predicted_label = model.config.id2label[prediction_id]

    return predicted_label

# ==============================================================================
#  Streamlit App User Interface
# ==============================================================================
st.set_page_config(page_title="Dark Pattern Detector", page_icon="🕵️", layout="wide")

st.title("🕵️ Dark Pattern Detector")
st.markdown("This tool uses a BERT-based AI model to analyze text and identify manipulative design patterns (dark patterns) commonly found on websites and apps.")

st.write("") # Add a little space

# Create two columns for a cleaner layout
col1, col2 = st.columns([2, 1])

with col1:
    text_input = st.text_area(
        "Enter text from a website or app to analyze:",
        height=150,
        placeholder="e.g., 'Only 2 items left in stock, order now!' or 'This offer expires in 5 minutes!'"
    )

    if st.button("Analyze Text", type="primary"):
        if text_input and model:
            with st.spinner('AI is thinking...'):
                result = predict_dark_pattern(text_input)
                st.info(f"**Detected Pattern:** `{result}`")
        elif not model:
            st.error("Cannot analyze text because the model is not loaded.")
        else:
            st.warning("Please enter some text to analyze.")

with col2:
    st.subheader("What are Dark Patterns?")
    st.markdown("""
    Dark patterns are tricks used in user interfaces to make you do things you didn't mean to, like buying something or signing up for a service. This tool helps identify them based on the language used.

    **Common Types:**
    - **Scarcity:** Suggesting an item is in limited supply.
    - **Urgency:** Using time-based pressure (e.g., countdowns).
    - **Social Proof:** Implying high demand from others.
    - **Misdirection:** Guiding you toward an unintended choice.
    """)
