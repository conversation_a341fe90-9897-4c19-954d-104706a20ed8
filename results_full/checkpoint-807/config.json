{"architectures": ["BertForSequenceClassification"], "attention_probs_dropout_prob": 0.1, "classifier_dropout": null, "dtype": "float32", "gradient_checkpointing": false, "hidden_act": "gelu", "hidden_dropout_prob": 0.1, "hidden_size": 768, "id2label": {"0": "Social Proof", "1": "Misdirection", "2": "Urgency", "3": "Forced Action", "4": "Obstruction", "5": "Sneaking", "6": "Scarcity"}, "initializer_range": 0.02, "intermediate_size": 3072, "label2id": {"Forced Action": 3, "Misdirection": 1, "Obstruction": 4, "Scarcity": 6, "Sneaking": 5, "Social Proof": 0, "Urgency": 2}, "layer_norm_eps": 1e-12, "max_position_embeddings": 512, "model_type": "bert", "num_attention_heads": 12, "num_hidden_layers": 12, "pad_token_id": 0, "position_embedding_type": "absolute", "problem_type": "single_label_classification", "transformers_version": "4.56.0", "type_vocab_size": 2, "use_cache": true, "vocab_size": 30522}