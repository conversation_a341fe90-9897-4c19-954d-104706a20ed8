{"best_global_step": 387, "best_metric": 0.16573430597782135, "best_model_checkpoint": "./results_full/checkpoint-387", "epoch": 1.0, "eval_steps": 500, "global_step": 387, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.12919896640826872, "grad_norm": 8.644737243652344, "learning_rate": 4.9000000000000005e-06, "loss": 1.9074, "step": 50}, {"epoch": 0.25839793281653745, "grad_norm": 8.123724937438965, "learning_rate": 9.900000000000002e-06, "loss": 1.5893, "step": 100}, {"epoch": 0.3875968992248062, "grad_norm": 13.546333312988281, "learning_rate": 1.49e-05, "loss": 1.081, "step": 150}, {"epoch": 0.5167958656330749, "grad_norm": 6.913139343261719, "learning_rate": 1.9900000000000003e-05, "loss": 0.6331, "step": 200}, {"epoch": 0.6459948320413437, "grad_norm": 4.4454345703125, "learning_rate": 2.4900000000000002e-05, "loss": 0.3193, "step": 250}, {"epoch": 0.7751937984496124, "grad_norm": 0.34606993198394775, "learning_rate": 2.9900000000000002e-05, "loss": 0.2323, "step": 300}, {"epoch": 0.9043927648578811, "grad_norm": 0.12485728412866592, "learning_rate": 3.49e-05, "loss": 0.2082, "step": 350}, {"epoch": 1.0, "eval_accuracy": 0.9573643410852714, "eval_f1": 0.9531702923496929, "eval_loss": 0.16573430597782135, "eval_precision": 0.9494238474536664, "eval_recall": 0.9573643410852714, "eval_runtime": 21.9554, "eval_samples_per_second": 35.253, "eval_steps_per_second": 4.418, "step": 387}], "logging_steps": 50, "max_steps": 1161, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 203527365070848.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}