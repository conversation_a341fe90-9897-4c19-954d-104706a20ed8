{"best_global_step": 538, "best_metric": 0.10188975930213928, "best_model_checkpoint": "./results_full/checkpoint-538", "epoch": 2.0, "eval_steps": 500, "global_step": 538, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.18587360594795538, "grad_norm": 16.16817283630371, "learning_rate": 4.9000000000000005e-06, "loss": 1.9853, "step": 50}, {"epoch": 0.37174721189591076, "grad_norm": 17.553125381469727, "learning_rate": 9.900000000000002e-06, "loss": 1.4527, "step": 100}, {"epoch": 0.5576208178438662, "grad_norm": 12.815323829650879, "learning_rate": 1.49e-05, "loss": 0.8673, "step": 150}, {"epoch": 0.7434944237918215, "grad_norm": 11.041848182678223, "learning_rate": 1.9900000000000003e-05, "loss": 0.4757, "step": 200}, {"epoch": 0.929368029739777, "grad_norm": 1.038743257522583, "learning_rate": 2.4900000000000002e-05, "loss": 0.1904, "step": 250}, {"epoch": 1.0, "eval_accuracy": 0.9572490706319703, "eval_f1": 0.9518098693731268, "eval_loss": 0.17050519585609436, "eval_precision": 0.9492597296281967, "eval_recall": 0.9572490706319703, "eval_runtime": 16.3788, "eval_samples_per_second": 32.847, "eval_steps_per_second": 4.152, "step": 269}, {"epoch": 1.1152416356877324, "grad_norm": 1.6055206060409546, "learning_rate": 2.9900000000000002e-05, "loss": 0.1671, "step": 300}, {"epoch": 1.3011152416356877, "grad_norm": 0.07969143986701965, "learning_rate": 3.49e-05, "loss": 0.0638, "step": 350}, {"epoch": 1.486988847583643, "grad_norm": 0.5083152055740356, "learning_rate": 3.99e-05, "loss": 0.0837, "step": 400}, {"epoch": 1.6728624535315983, "grad_norm": 48.325599670410156, "learning_rate": 4.49e-05, "loss": 0.143, "step": 450}, {"epoch": 1.858736059479554, "grad_norm": 3.84291410446167, "learning_rate": 4.99e-05, "loss": 0.1464, "step": 500}, {"epoch": 2.0, "eval_accuracy": 0.9776951672862454, "eval_f1": 0.9752303698602537, "eval_loss": 0.10188975930213928, "eval_precision": 0.9745440720385691, "eval_recall": 0.9776951672862454, "eval_runtime": 16.577, "eval_samples_per_second": 32.455, "eval_steps_per_second": 4.102, "step": 538}], "logging_steps": 50, "max_steps": 807, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 283120205107200.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}