{"best_global_step": 1161, "best_metric": 0.08377889543771744, "best_model_checkpoint": "./results_full/checkpoint-1161", "epoch": 3.0, "eval_steps": 500, "global_step": 1161, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.12919896640826872, "grad_norm": 8.644737243652344, "learning_rate": 4.9000000000000005e-06, "loss": 1.9074, "step": 50}, {"epoch": 0.25839793281653745, "grad_norm": 8.123724937438965, "learning_rate": 9.900000000000002e-06, "loss": 1.5893, "step": 100}, {"epoch": 0.3875968992248062, "grad_norm": 13.546333312988281, "learning_rate": 1.49e-05, "loss": 1.081, "step": 150}, {"epoch": 0.5167958656330749, "grad_norm": 6.913139343261719, "learning_rate": 1.9900000000000003e-05, "loss": 0.6331, "step": 200}, {"epoch": 0.6459948320413437, "grad_norm": 4.4454345703125, "learning_rate": 2.4900000000000002e-05, "loss": 0.3193, "step": 250}, {"epoch": 0.7751937984496124, "grad_norm": 0.34606993198394775, "learning_rate": 2.9900000000000002e-05, "loss": 0.2323, "step": 300}, {"epoch": 0.9043927648578811, "grad_norm": 0.12485728412866592, "learning_rate": 3.49e-05, "loss": 0.2082, "step": 350}, {"epoch": 1.0, "eval_accuracy": 0.9573643410852714, "eval_f1": 0.9531702923496929, "eval_loss": 0.16573430597782135, "eval_precision": 0.9494238474536664, "eval_recall": 0.9573643410852714, "eval_runtime": 21.9554, "eval_samples_per_second": 35.253, "eval_steps_per_second": 4.418, "step": 387}, {"epoch": 1.0335917312661498, "grad_norm": 2.898517608642578, "learning_rate": 3.99e-05, "loss": 0.1715, "step": 400}, {"epoch": 1.1627906976744187, "grad_norm": 0.06219932809472084, "learning_rate": 4.49e-05, "loss": 0.1539, "step": 450}, {"epoch": 1.2919896640826873, "grad_norm": 0.11905112117528915, "learning_rate": 4.99e-05, "loss": 0.1186, "step": 500}, {"epoch": 1.421188630490956, "grad_norm": 0.03962838649749756, "learning_rate": 4.629349470499244e-05, "loss": 0.096, "step": 550}, {"epoch": 1.550387596899225, "grad_norm": 10.981474876403809, "learning_rate": 4.251134644478064e-05, "loss": 0.1579, "step": 600}, {"epoch": 1.6795865633074936, "grad_norm": 12.5294828414917, "learning_rate": 3.8729198184568835e-05, "loss": 0.1563, "step": 650}, {"epoch": 1.8087855297157622, "grad_norm": 30.51628875732422, "learning_rate": 3.494704992435704e-05, "loss": 0.1574, "step": 700}, {"epoch": 1.937984496124031, "grad_norm": 0.08738585561513901, "learning_rate": 3.116490166414523e-05, "loss": 0.1171, "step": 750}, {"epoch": 2.0, "eval_accuracy": 0.9780361757105943, "eval_f1": 0.976694178702758, "eval_loss": 0.0885305106639862, "eval_precision": 0.9767670899219697, "eval_recall": 0.9780361757105943, "eval_runtime": 22.0354, "eval_samples_per_second": 35.125, "eval_steps_per_second": 4.402, "step": 774}, {"epoch": 2.0671834625322996, "grad_norm": 0.02249150723218918, "learning_rate": 2.7382753403933437e-05, "loss": 0.0962, "step": 800}, {"epoch": 2.1963824289405687, "grad_norm": 0.01642044261097908, "learning_rate": 2.3600605143721636e-05, "loss": 0.0362, "step": 850}, {"epoch": 2.3255813953488373, "grad_norm": 0.014478042721748352, "learning_rate": 1.9818456883509835e-05, "loss": 0.0379, "step": 900}, {"epoch": 2.454780361757106, "grad_norm": 0.10618432611227036, "learning_rate": 1.6036308623298037e-05, "loss": 0.0172, "step": 950}, {"epoch": 2.5839793281653747, "grad_norm": 0.0141924899071455, "learning_rate": 1.2254160363086234e-05, "loss": 0.0543, "step": 1000}, {"epoch": 2.7131782945736433, "grad_norm": 0.014444146305322647, "learning_rate": 8.472012102874432e-06, "loss": 0.0032, "step": 1050}, {"epoch": 2.842377260981912, "grad_norm": 0.013558032922446728, "learning_rate": 4.689863842662633e-06, "loss": 0.0072, "step": 1100}, {"epoch": 2.971576227390181, "grad_norm": 0.04596357420086861, "learning_rate": 9.077155824508321e-07, "loss": 0.0749, "step": 1150}, {"epoch": 3.0, "eval_accuracy": 0.979328165374677, "eval_f1": 0.977641358007458, "eval_loss": 0.08377889543771744, "eval_precision": 0.9762142460100486, "eval_recall": 0.979328165374677, "eval_runtime": 22.0753, "eval_samples_per_second": 35.062, "eval_steps_per_second": 4.394, "step": 1161}], "logging_steps": 50, "max_steps": 1161, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 610582095212544.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}