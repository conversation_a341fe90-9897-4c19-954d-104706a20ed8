import os
os.environ["CUDA_VISIBLE_DEVICES"] = ""  # Force CPU on Mac

import pandas as pd
import torch
from sklearn.model_selection import train_test_split
from transformers import BertTokenizer, BertForSequenceClassification, Trainer, TrainingArguments
from torch.utils.data import Dataset
import re
from sklearn.metrics import accuracy_score, precision_recall_fscore_support

# --------------------------
# Step 1: Dataset Class
# --------------------------
class DarkPatternDataset(Dataset):
    def __init__(self, encodings, labels):
        self.encodings = encodings
        self.labels = labels

    def __getitem__(self, idx):
        item = {key: torch.tensor(val[idx]) for key, val in self.encodings.items()}
        item['labels'] = torch.tensor(self.labels[idx])
        return item

    def __len__(self):
        return len(self.labels)

# --------------------------
# Step 2: Text Preprocessing
# --------------------------
def clean_text(text):
    text = str(text).lower()
    text = re.sub(r'<.*?>', '', text)
    text = re.sub(r'[^a-z\s]', '', text)
    tokens = text.split()
    return ' '.join(tokens)

# --------------------------
# Step 3: Metrics Function
# --------------------------
def compute_metrics(pred):
    labels = pred.label_ids
    preds = pred.predictions.argmax(-1)
    precision, recall, f1, _ = precision_recall_fscore_support(labels, preds, average='weighted')
    acc = accuracy_score(labels, preds)
    return {'accuracy': acc, 'f1': f1, 'precision': precision, 'recall': recall}

# --------------------------
# Step 4: Load Dataset
# --------------------------
print("Loading dataset...")
df = pd.read_csv('combined_dark_patterns.csv')  # Ensure this file exists

labels = df['category'].unique().tolist()
label2id = {label: i for i, label in enumerate(labels)}
id2label = {i: label for i, label in enumerate(labels)}
df['label_id'] = df['category'].map(label2id)
df['processed_text'] = df['text'].apply(clean_text)
print("Dataset ready.")

# --------------------------
# Step 5: Train/Validation Split
# --------------------------
train_texts, val_texts, train_labels, val_labels = train_test_split(
    df['processed_text'].tolist(),
    df['label_id'].tolist(),
    test_size=0.2,
    random_state=42,
    stratify=df['label_id']
)

# --------------------------
# Step 6: Tokenization
# --------------------------
tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
train_encodings = tokenizer(train_texts, truncation=True, padding=True, max_length=128)
val_encodings = tokenizer(val_texts, truncation=True, padding=True, max_length=128)

train_dataset = DarkPatternDataset(train_encodings, train_labels)
val_dataset = DarkPatternDataset(val_encodings, val_labels)

# --------------------------
# Step 7: Load BERT Model
# --------------------------
model = BertForSequenceClassification.from_pretrained(
    'bert-base-uncased',
    num_labels=len(labels),
    id2label=id2label,
    label2id=label2id
)

# --------------------------
# Step 8: TrainingArguments (CPU-ready)
# --------------------------
training_args = TrainingArguments(
    output_dir='./results',
    num_train_epochs=3,
    per_device_train_batch_size=8,
    per_device_eval_batch_size=8,
    logging_dir='./logs',
    logging_steps=10,
    save_strategy="no",
    evaluation_strategy="no",
    load_best_model_at_end=False
)

# --------------------------
# Step 9: Trainer
# --------------------------
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_dataset,
    eval_dataset=val_dataset,
    compute_metrics=compute_metrics
)

# --------------------------
# Step 10: Train
# --------------------------
print("Training model...")
trainer.train()
print("Training complete!")

# --------------------------
# Step 11: Save Model
# --------------------------
trainer.save_model('./final_model')
tokenizer.save_pretrained('./final_model')
print("Model saved.")

# --------------------------
# Step 12: Prediction Function
# --------------------------
def predict_dark_pattern(text):
    model.eval()
    cleaned_text = clean_text(text)
    inputs = tokenizer(cleaned_text, return_tensors='pt', truncation=True, padding=True)
    with torch.no_grad():
        outputs = model(**inputs)
    pred_id = torch.argmax(outputs.logits, dim=-1).item()
    return id2label[pred_id]

# --------------------------
# Step 13: Test Prediction
# --------------------------
samples = [
    "Hurry up! This special offer expires in just 5 minutes!",
    "There are only 2 items left in stock, order now before it's gone!",
    "Over 500 people have bought this today.",
    "No thanks, I prefer paying full price."
]

for s in samples:
    print(f"\nText: '{s}'")
    print(f"Predicted pattern: {predict_dark_pattern(s)}")
